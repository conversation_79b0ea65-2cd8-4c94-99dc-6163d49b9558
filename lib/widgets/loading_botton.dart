import 'package:flutter/material.dart';

class LoadingButton extends StatefulWidget {
  final Future<void> Function() onPressed;
  final String label;
  final ButtonStyle? style;
  final bool enableTooltip; // 是否启用tooltip提示

  const LoadingButton({
    super.key,
    required this.onPressed,
    required this.label,
    this.style,
    this.enableTooltip = true, // 默认启用tooltip
  });

  @override
  State<LoadingButton> createState() => _LoadingButtonState();
}

class _LoadingButtonState extends State<LoadingButton> {
  bool _isLoading = false;
  bool _isHovered = false;
  bool _showTooltip = false;

  Future<void> _handlePress() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await widget.onPressed();
    } catch (e) {
      print('Error during button action: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onEnter() {
    setState(() => _isHovered = true);
    // 只有启用tooltip且文本较长时才显示
    if (widget.enableTooltip && widget.label.length > 10) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_isHovered && mounted) {
          setState(() => _showTooltip = true);
        }
      });
    }
  }

  void _onExit() {
    setState(() {
      _isHovered = false;
      _showTooltip = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        MouseRegion(
          onEnter: (_) => _onEnter(),
          onExit: (_) => _onExit(),
          child: TextButton(
            onPressed: _isLoading ? null : _handlePress,
            style: widget.style ?? TextButton.styleFrom(
              backgroundColor: const Color(0xFFF3F4F8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ).copyWith(
              // 添加鼠标悬停时的背景色
              overlayColor:
                  WidgetStateProperty.resolveWith<Color?>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.hovered)) {
                    // 悬停时的背景色，这里使用稍深一点的紫色
                    return Theme.of(context).colorScheme.primary.withValues(alpha: 1);
                  }
                  return null; // 返回 null 使用默认颜色
                },
              ),
              foregroundColor: WidgetStateProperty.resolveWith<Color?>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.hovered)) {
                    return Colors.white;
                  }
                  return const Color(0xFF333333);
                },
              ),
            ),
            child: _isLoading
                ? const SizedBox(
              height: 16,
              width: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
              ),
            )
                : Text(
              widget.label, 
              style: const TextStyle(fontSize: 12),
              maxLines: 1, // 限制为单行
              overflow: TextOverflow.ellipsis, // 超出显示省略号
              textAlign: TextAlign.left,
            ),
          ),
        ),
        // 自定义tooltip - 只在启用且文本较长时显示
        if (_showTooltip && widget.enableTooltip && widget.label.length > 10)
          Positioned(
            bottom: 60, // 在按钮上方显示
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[700], // 更柔和的灰色背景
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: const [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  widget.label,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9), // 90%透明度的白色文字
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
