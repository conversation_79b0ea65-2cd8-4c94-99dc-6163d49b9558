import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CommentEditDialog extends HookConsumerWidget {
  final int windowId;
  final String initialComment;
  final VoidCallback? onCommentUpdated;

  const CommentEditDialog({
    super.key,
    required this.windowId,
    required this.initialComment,
    this.onCommentUpdated,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final commentController = useTextEditingController(text: initialComment);
    final isLoading = useState(false);

    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(30),
        child: Column(
          spacing: 20,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '备注',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextField(
              controller: commentController,
              style: const TextStyle(fontSize: 14),
              decoration: const InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Color(0xFF0C75F8)),
                ),
                hintText: '请输入备注',
              ),
              maxLines: 5, // 允许多行输入
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                PrimaryButton(
                  onPressed: isLoading.value
                      ? () {}
                      : () async {
                          isLoading.value = true;
                          try {
                            // 调用更新备注的方法
                            await ref
                                .read(browserWindowControllerProvider.notifier)
                                .updateBrowserWindowComment(
                                  windowId,
                                  commentController.text,
                                );

                            if (context.mounted) {
                              SnackBarUtil().showSuccess(context, '备注更新成功');
                            }

                            onCommentUpdated?.call();

                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                          } catch (e) {
                            if (context.mounted) {
                              SnackBarUtil().showError(context, '更新备注失败: $e');
                            }
                          } finally {
                            isLoading.value = false;
                          }
                        },
                  child: isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
