import 'dart:convert';

import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:frontend_re/core/utils/path.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_table_controller.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';
import 'package:frontend_re/domain/models/browser_window_model.dart';
import 'package:frontend_re/features/workbench/views/components/browser_delete_dialog.dart';
import 'package:frontend_re/features/workbench/views/components/comment_edit_dialog.dart';
import 'package:frontend_re/features/workbench/views/components/proxy_update_dialog.dart';
import 'package:frontend_re/features/workbench/views/components/sort_edit_dialog.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/widgets/custom_edit_button.dart';
import 'package:frontend_re/widgets/data_table_widget.dart';
import 'package:frontend_re/widgets/loading_botton.dart';
import 'package:frontend_re/widgets/proxy_table_btn.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:browser_tool/browser_tool.dart';

class BrowserTable extends HookConsumerWidget {
  const BrowserTable({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //监听浏览器窗口数据
    final browserWindowsState = ref.watch(browserWindowControllerProvider);
    final controller = ref.read(browserWindowControllerProvider.notifier);

    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              Expanded(
                child: browserWindowsState.when(
                  data: (browserWindowModel) => 
                  DataTableWidget(
                    headingTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    dataTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93)),
                    groupHeaderTextStyle: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
                    minWidth: 1700,
                    showCheckbox: true,
                    columnSpacing: 0,
                    groupBy: (window) => window.groupName,
                    groupHeaderBuilder: (groupKey, groupSize, isExpanded) {
                      return DataRow(cells: [
                        DataCell(Text(groupKey)),
                      ]);
                    },
                    noDataImagePath: 'assets/images/proxy/NoData.png',
                    noDataText: '暂无环境数据',
                    noDataButtonText: '添加环境',
                    noDataButtonEvent: () {
                      context.pushNamed('addBrowser');
                    },
                    onSelectionChanged: (selectedRows) {
                      // selectedRows 是选中行的索引集合 (Set<int>)
                      // 由于数据被分组显示，需要特殊处理索引映射
                      final windows = browserWindowModel.windows ?? [];
                      
                      // 按照与DataTableWidget相同的逻辑创建分组数据
                      final groupedData = <String, List<BrowserWindowItem>>{};
                      for (var window in windows) {
                        groupedData.putIfAbsent(window.groupName, () => []).add(window);
                      }
                      
                      // 按分组重新排列数据，与UI显示顺序保持一致
                      final reorderedWindows = <BrowserWindowItem>[];
                      groupedData.forEach((groupName, groupWindows) {
                        reorderedWindows.addAll(groupWindows);
                      });
                      
                      // 根据重新排列后的数据获取选中的ID
                      final selectedIds = selectedRows
                          .where((index) => index < reorderedWindows.length)
                          .map((index) => reorderedWindows[index].id)
                          .toList();
                      
                      // 更新控制器中的选中状态
                      ref.read(browserTableControllerProvider.notifier).updateSelectedWindowIds(selectedIds);
                      print('选中的窗口ID: $selectedIds');
                    },
                    enablePagination: true,
                    totalCount: browserWindowModel.total,
                    pageSize: 10,
                    currentPage: 1,
                    onPageChanged: (page, pageSize) {
                      controller.loadBrowserWindows(offset: page, pageSize: pageSize);
                    },
                    columns: const [
                      DataColumn2(label: Text('编号/窗口名称'), size: ColumnSize.M),
                      DataColumn2(label: Text('操作'), fixedWidth: 220,headingRowAlignment: MainAxisAlignment.end),
                      DataColumn2(label: Text('账号平台'), size: ColumnSize.L),
                      DataColumn2(label: Text('代理IP'), size: ColumnSize.M),
                      DataColumn2(label: Text('标签'), size: ColumnSize.M),
                      DataColumn2(label: Text('备注'), size: ColumnSize.M),
                      DataColumn2(label: Text('云空间'), size: ColumnSize.M),
                      DataColumn2(label: Text('创建时间'), size: ColumnSize.M),
                      DataColumn2(label: Text('更新时间'), size: ColumnSize.M),
                    ],
                    data: browserWindowModel.windows ?? [],
                    rowBuilder: (window, index, isHovered) {
                      return DataRow2(cells: [
                        DataCell(
                          Row(
                            spacing: 4,
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                onTap: () {
                                  // 显示编号编辑对话框
                                  showDialog(
                                    context: context,
                                    builder: (context) => SortEditDialog(
                                      windowId: window.id,
                                      initialSort: window.sort,
                                      onSortUpdated: () {
                                        // 编号更新后刷新列表
                                        controller.loadBrowserWindows();
                                      },
                                    ),
                                  );
                                },
                                hoverColor: const Color(0xFFE8F1FF),
                                borderRadius: BorderRadius.circular(4),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF3F4F8),
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(color: const Color(0xFFEDEDF0), width: 1),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  child: Text(window.sort.toString(), style: const TextStyle(fontSize: 12, color: Color(0xFF8D8E93))),
                                ),
                              ),
                              Text(window.name, style: const TextStyle(fontSize: 14, color: Color(0xFF8D8E93))),
                            ],
                          )
                        ),
                        DataCell(_buildStartButton(context, window, controller, ref),),
                        DataCell(Text(window.platform, style: const TextStyle(fontSize: 12))),
                        DataCell(_buildProxyInfo(context, window.proxy.name, controller, window),),
                        DataCell(_buildTagsWidget(window.tag)), // 使用新的标签展示组件
                        DataCell(_buildCommentWidget(context, window, controller)),
                        DataCell(_cloudStorageBadge(size: window.size ?? 0)),
                        DataCell(Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(window.createdAt))),
                        DataCell(Text(DateFormat('yyyy-MM-dd HH:mm:ss').format(window.updatedAt))),
                      ]
                    );
                  },
                  ),
                  loading: () => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  error: (error, stack) => Center(
                    child: Text('加载失败: $error'),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // 代理信息展示组件
  Widget _buildProxyInfo(BuildContext context, String proxyInfo, BrowserWindowController controller, BrowserWindowItem window) {
    return ProxyTableBtn(
      onPressed: () async {
        try {
          // 获取当前窗口详细信息
          final browserWindow = await controller.getBrowserWindowById(window.id);

          if (browserWindow != null && context.mounted) {
            showDialog(
              context: context,
              builder: (context) => ProxyUpdateDialog(
                windowId: browserWindow.id,
                onProxyUpdated: () {
                  // 代理更新后刷新列表
                  controller.loadBrowserWindows();
                },
              ),
            );
          }
        } catch (e) {
          if (context.mounted) {
            SnackBarUtil().showError(context, '获取代理列表失败: $e');
          }
        }
      },
      label: window.proxy.name == '' ? "未绑定" : window.proxy.name, 
      proxyType: window.proxy.type,
    );
  }
  
  // 备注显示组件
  Widget _buildCommentWidget(BuildContext context, BrowserWindowItem window,BrowserWindowController controller) {
    final comment = window.comment ?? '';
    
    return InkWell(
      onTap: () {
        // 显示编辑对话框
        showDialog(
          context: context,
          builder: (context) => CommentEditDialog(
            windowId: window.id,
            initialComment: comment,
            onCommentUpdated: () {
              // 备注更新后刷新列表
              controller.loadBrowserWindows();
            },
          ),
        );
      },
      hoverColor: const Color(0xFFE8F1FF), // 鼠标悬停时的颜色
      borderRadius: BorderRadius.circular(4), // 圆角边框
      child: Container(
        constraints: const BoxConstraints(maxWidth: 150), // 限制最大宽度
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // 添加内边距
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFEDEDF0), width: 1), // 添加边框
          borderRadius: BorderRadius.circular(4), // 圆角边框
          color: const Color(0xFFF3F4F8), // 浅色背景
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: Text(
                comment.isEmpty ? '添加备注' : comment,
                overflow: TextOverflow.ellipsis, // 文本溢出时显示省略号
                maxLines: 2, // 最多显示两行
                style: TextStyle(
                  color: comment.isEmpty ? const Color(0xFF0C75F8) : const Color(0xFF8D8E93),
                  fontSize: 12,
                  height: 1.3, // 行高调整为更合适的值
                ),
              ),
            ),
            const SizedBox(width: 4),
            const Icon(
              Icons.edit_outlined,
              size: 14,
              color: Color(0xFF0C75F8),
            ),
          ],
        ),
      ),
    );
  }

  // 云空间显示组件
  Widget _cloudStorageBadge({required int size}) {
    // 使用应用主色调
    const Color baseColor = Color(0xFF0C75F8); // 应用的主色调
    
    // 根据大小调整透明度，而不是完全改变颜色
    double opacity;
    if (size < 100) {
      opacity = 0.7; // 较小空间
    } else if (size < 500) {
      opacity = 0.8; // 中等空间
    } else {
      opacity = 0.9; // 较大空间
    }
    
    final Color backgroundColor = baseColor.withValues(alpha: opacity);

    // 格式化显示
    String displayText;
    if (size < 1024) {
      displayText = '$size MB';
    } else {
      double gbSize = size / 1024;
      displayText = '${gbSize.toStringAsFixed(1)} GB';
    }

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // TODO: 添加点击事件处理逻辑
          print('云空间被点击: $displayText');
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F8), // 使用应用的surface颜色
            borderRadius: BorderRadius.circular(4), // 减小圆角半径，更简约
            border: Border.all(color: const Color(0xFFEDEDF0), width: 1), // 使用应用的secondaryColor
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.cloud,
                size: 14,
                color: backgroundColor,
              ),
              const SizedBox(width: 4),
              Text(
                displayText,
                style: const TextStyle(
                  color: Color(0xFF8D8E93), // 使用应用的onSecondaryColor
                  fontWeight: FontWeight.normal,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 操作按钮组
  Widget _buildStartButton(BuildContext context, BrowserWindowItem window, BrowserWindowController controller, WidgetRef ref) {
    return Row(
      spacing: 6,
      children: [
        // 启动/停止环境按钮 - 使用简单的管理器
        _SimpleBrowserButton(
          window: window,
          controller: controller,
        ),
        // 更新环境按钮
        CustomEditButton(
          iconPath: 'assets/svg/open.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '更新环境',
          onPressed: () async {
            try {
              // 调用获取单个窗口方法
              final browserWindow = await controller.getBrowserWindowById(window.id);

              if (browserWindow != null) {
                // 解析获取到的 `parameters` 传递给对话框
                Map<String, dynamic> config = jsonDecode(browserWindow.parameters);
                config['id'] = window.id;
                String path = await getPath();
                // 尝试获取Cookie，如果失败则不赋值
                final cookieResult = BrowserTool.getCookie('$path${browserWindow.id}/');
                if (cookieResult != null && !cookieResult.startsWith('获取Cookie失败:')) {
                  config['ui_cookie'] = cookieResult;
                  print('Successfully got cookie for browser ${browserWindow.id}');
                } else {
                  print('Failed to get cookie for browser ${browserWindow.id}: $cookieResult');
                  // 不赋值给 config['ui_cookie']，保持原有值或为空
                }
                // config['ui_cookie'] = await getCookie('$path${browserWindow.id}/');
                config['ui_comment'] = browserWindow.comment;
                config['ui_tag'] = _convertTagsToString(browserWindow.tag);
                
                // 添加分组信息
                config['group_id'] = browserWindow.groupId;
                config['group_name'] = browserWindow.groupName;

                
                if (context.mounted) {
                  context.pushNamed('addBrowser', extra: jsonEncode(config));
                }
              } else {
                if (context.mounted) {
                  SnackBarUtil().showWarning(context, '未找到该窗口信息');
                }
              }
            } catch (e) {
              if (context.mounted) {
                SnackBarUtil().showError(context, '获取窗口信息失败: $e');
              }
            }
          },
        ),
        // 删除环境按钮
        CustomEditButton(
          iconPath: 'assets/svg/delete.svg',
          iconWidth: 16,
          iconHeight: 16,
          tooltip: '删除环境',
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => ConfirmDeleteDialog(
                ids: [window.id], // 传入要删除的 ID 列表
                name: window.name, // 传入窗口名称
              ),
            );
          },
        ),
        // 环境空间按钮
        // CustomEditButton(
        //   iconPath: 'assets/svg/output.svg',
        //   iconWidth: 16,
        //   iconHeight: 16,
        //   tooltip: '环境空间',
        //   onPressed: () {},
        // ),
      ],
    );
  }

  // 将标签JSON数组转换为逗号分隔的字符串
  String _convertTagsToString(String tagJson) {
    if (tagJson.isEmpty) {
      return '';
    }
    
    try {
      // 尝试解析 JSON 字符串
      List<dynamic> tags = jsonDecode(tagJson);
      // 将数组元素转换为字符串并用逗号连接
      return tags.map((tag) => tag.toString()).join(',');
    } catch (e) {
      // 如果解析失败，说明可能是旧格式的逗号分隔字符串，直接返回
      return tagJson;
    }
  }

  // 新增：标签展示组件
  Widget _buildTagsWidget(String tagJson) {
    List<dynamic> tags = [];
    try {
      // 尝试解析 JSON 字符串
      tags = jsonDecode(tagJson);
    } catch (e) {
      // 如果解析失败，尝试将其作为逗号分隔的字符串处理（兼容旧数据）
      if (tagJson.isNotEmpty) {
        tags = tagJson.split(',');
      }
    }

    if (tags.isEmpty) {
      return const Text('无标签');
    }

    return Wrap(
      spacing: 4,
      runSpacing: 4,
      children: tags.map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F8),  // 使用应用的surface颜色
            borderRadius: BorderRadius.circular(4),  // 减小圆角半径，更简约
            border: Border.all(color: const Color(0xFFEDEDF0), width: 1),  // 使用应用的secondaryColor作为边框
          ),
          child: Text(
            tag.toString(),
            style: const TextStyle(
              color: Color(0xFF8D8E93),  // 使用应用的onSecondaryColor
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
          ),
        );
      }).toList(),
    );
  }


}

/// 简单的浏览器按钮组件，使用StatefulWidget来管理状态
class _SimpleBrowserButton extends StatefulWidget {
  final BrowserWindowItem window;
  final BrowserWindowController controller;

  const _SimpleBrowserButton({
    required this.window,
    required this.controller,
  });

  @override
  State<_SimpleBrowserButton> createState() => _SimpleBrowserButtonState();
}

class _SimpleBrowserButtonState extends State<_SimpleBrowserButton> {
  SimpleBrowserManager get _manager => SimpleBrowserManager.instance;
  late void Function(SimpleBrowserInstance) _listener;

  @override
  void initState() {
    super.initState();
    
    // 确保管理器中有此浏览器实例
    _manager.createStoppedInstance(widget.window.id.toString(), widget.window.name);
    
    // 添加状态变化监听器 - 只监听自己的状态变化
    _listener = (browser) {
      if (browser.id == widget.window.id.toString()) {
        debugPrint('🔔 Listener for ${widget.window.id}: ${browser.status.name}');
        if (mounted) {
          setState(() {
            // 强制重建UI
          });
        }
      }
    };
    _manager.addListener(_listener);
  }

  @override
  void dispose() {
    _manager.removeListener(_listener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final browser = _manager.getBrowser(widget.window.id.toString());
    final isRunning = browser?.isRunning ?? false;
    
    debugPrint('🎮 Button ${widget.window.id}: ${isRunning ? "停止" : "启动"} (status: ${browser?.status.name ?? "null"})');
    
    return LoadingButton(
      onPressed: () async {
        debugPrint('🔘 Button CLICKED for ${widget.window.id}, current isRunning: $isRunning');
        if (isRunning) {
          // 停止浏览器
          await _stopBrowser(context);
        } else {
          // 启动浏览器
          await _startBrowser(context);
        }
      },
      label: isRunning ? '停止' : '启动',
    );
  }



  /// 启动浏览器
  Future<void> _startBrowser(BuildContext context) async {
    debugPrint('🚀 Starting browser ${widget.window.id}...');
    try {
      final browserWindow = await widget.controller.getBrowserWindowById(widget.window.id);

      if (browserWindow != null) {
        Map<String, dynamic> config = jsonDecode(browserWindow.parameters);
        config['id'] = widget.window.id.toString();

        // 设置代理配置
        final proxyTypeMap = {
          0: 'none',
          1: 'http',
          2: 'https',
          3: 'socks5',
          4: 'tuic',
        };
        config['proxy_type'] = proxyTypeMap[browserWindow.proxy.type] ?? 'unknown';
        config['proxy_address'] = browserWindow.proxy.address;
        config['proxy_port'] = browserWindow.proxy.port;
        config['proxy_username'] = browserWindow.proxy.username;
        config['proxy_password'] = browserWindow.proxy.password;

        // 添加编号
        config['sort'] = browserWindow.sort;

        String path = await getPath();
        String configPath = '$path${browserWindow.id}';

        final success = await _manager.launchBrowser(
          id: widget.window.id.toString(),
          name: browserWindow.name,
          configPath: configPath,
          config: config,
        );

        if (context.mounted) {
          if (success) {
            SnackBarUtil().showSuccess(context, '浏览器 ${browserWindow.name} 启动成功');
          } else {
            SnackBarUtil().showError(context, '浏览器 ${browserWindow.name} 启动失败');
          }
        }
      } else {
        if (context.mounted) {
          SnackBarUtil().showWarning(context, '未找到该窗口信息');
        }
      }
    } catch (e) {
      if (context.mounted) {
        SnackBarUtil().showError(context, '启动失败: $e');
      }
    }
  }

  /// 停止浏览器
  Future<void> _stopBrowser(BuildContext context) async {
    debugPrint('🛑 Stopping browser ${widget.window.id}...');
    try {
      final success = await _manager.stopBrowser(widget.window.id.toString());

      if (context.mounted) {
        if (success) {
          SnackBarUtil().showSuccess(context, '浏览器 ${widget.window.name} 已停止');
        } else {
          SnackBarUtil().showError(context, '浏览器 ${widget.window.name} 停止失败');
        }
      }
    } catch (e) {
      if (context.mounted) {
        SnackBarUtil().showError(context, '停止失败: $e');
      }
    }
  }
}
