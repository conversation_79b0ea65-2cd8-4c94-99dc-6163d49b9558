import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/core/utils/ui/snack_bar_util.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SortEditDialog extends HookConsumerWidget {
  final int windowId;
  final int initialSort;
  final VoidCallback? onSortUpdated;

  const SortEditDialog({
    super.key,
    required this.windowId,
    required this.initialSort,
    this.onSortUpdated,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortController = useTextEditingController(text: initialSort.toString());
    final isLoading = useState(false);

    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.surfaceContainer,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 400,
        padding: const EdgeInsets.all(30),
        child: Column(
          spacing: 20,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '编辑编号',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextField(
              controller: sortController,
              style: const TextStyle(fontSize: 14),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10), // 限制最大位数
              ],
              decoration: const InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide(color: Color(0xFF0C75F8)),
                ),
                hintText: '请输入编号',
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                PrimaryButton(
                  onPressed: isLoading.value
                      ? () {}
                      : () async {
                          final sortText = sortController.text.trim();
                          if (sortText.isEmpty) {
                            SnackBarUtil().showError(context, '请输入编号');
                            return;
                          }
                          
                          final newSort = int.tryParse(sortText);
                          if (newSort == null) {
                            SnackBarUtil().showError(context, '请输入有效的数字');
                            return;
                          }
                          
                          if (newSort == initialSort) {
                            Navigator.of(context).pop();
                            return;
                          }

                          isLoading.value = true;
                          try {
                            // 调用更新编号的方法
                            await ref
                                .read(browserWindowControllerProvider.notifier)
                                .updateBrowserWindowSort(windowId, newSort);

                            if (context.mounted) {
                              SnackBarUtil().showSuccess(context, '编号更新成功');
                            }

                            onSortUpdated?.call();

                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                          } catch (e) {
                            if (context.mounted) {
                              SnackBarUtil().showError(context, '更新编号失败: $e');
                            }
                          } finally {
                            isLoading.value = false;
                          }
                        },
                  child: isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 
