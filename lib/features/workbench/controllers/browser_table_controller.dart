import 'dart:convert';
import 'package:frontend_re/features/workbench/models/browser_select_model.dart';
import 'package:frontend_re/features/workbench/controllers/browser_window_controller.dart';
import 'package:frontend_re/features/workbench/controllers/browser_management_controller.dart';
import 'package:frontend_re/features/workbench/controllers/simple_browser_manager.dart';
import 'package:frontend_re/core/utils/path.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

// 生成的代码将在此文件中
part 'browser_table_controller.g.dart';

@riverpod
class BrowserTableController extends _$BrowserTableController {
  @override
  BrowserSelectModel build() {
    // 表格选中的ids
    return const BrowserSelectModel(browserWindows: []);
  }

  bool get hasSelectedIds => state.selectedWindowIds.isNotEmpty;
  List<int> get selectedIds => state.selectedWindowIds;
  
  // 更新选中的窗口ID
  void updateSelectedWindowIds(List<int> ids) {
    state = state.copyWith(selectedWindowIds: ids);
  }

  // 清空状态
  void clearSelectedWindowIds() {
    state = state.copyWith(selectedWindowIds: []);
  }

  // 删除选中的窗口
  Future<String> deleteSelectedWindows() async {
    // 删除选中的窗口
    final ids = state.selectedWindowIds;
    // 删除窗口
    final res = await ref.read(browserWindowControllerProvider.notifier).deleteBrowserWindows(ids);
    // 删除后清空选中的ids
    clearSelectedWindowIds();
    return res;
  }

  // 批量打开选中的窗口
  Future<String> openSelectedWindows({bool clearSelection = false}) async {
    final selectedIds = state.selectedWindowIds;
    if (selectedIds.isEmpty) {
      throw Exception('请先选择要打开的窗口');
    }

    final controller = ref.read(browserWindowControllerProvider.notifier);
    final browserManager = SimpleBrowserManager.instance; // 使用新的SimpleBrowserManager
    final String path = await getPath();
    
    List<String> failedToGetConfig = [];
    List<String> successList = [];
    List<String> failureList = [];

    // 逐个启动浏览器，避免同时启动过多导致系统压力
    for (final windowId in selectedIds) {
      try {
        final browserWindow = await controller.getBrowserWindowById(windowId);
        
        if (browserWindow == null) {
          failedToGetConfig.add('窗口ID: $windowId - 未找到窗口信息');
          continue;
        }

        // 检查是否已在运行
        final existingBrowser = browserManager.getBrowser(windowId.toString());
        if (existingBrowser != null && existingBrowser.isRunning) {
          failedToGetConfig.add('窗口: ${browserWindow.name} - 已在运行中');
          continue;
        }

        // 解析配置参数
        Map<String, dynamic> config = jsonDecode(browserWindow.parameters);
        config['id'] = windowId.toString();

        // 设置代理配置
        final proxyTypeMap = {
          0: 'none',
          1: 'http',
          2: 'https',
          3: 'socks5',
          4: 'tuic',
        };
        config['proxy_type'] = proxyTypeMap[browserWindow.proxy.type] ?? 'unknown';
        config['proxy_address'] = browserWindow.proxy.address;
        config['proxy_port'] = browserWindow.proxy.port;
        config['proxy_username'] = browserWindow.proxy.username;
        config['proxy_password'] = browserWindow.proxy.password;

        // 添加编号
        config['sort'] = browserWindow.sort;

        // 使用新的SimpleBrowserManager启动浏览器
        final success = await browserManager.launchBrowser(
          id: windowId.toString(),
          name: browserWindow.name,
          configPath: '$path${browserWindow.id}',
          config: config,
        );

        if (success) {
          successList.add(browserWindow.name);
        } else {
          failureList.add('${browserWindow.name} - 启动失败');
        }

        // 添加延迟避免同时启动太多浏览器造成系统压力
        await Future.delayed(const Duration(milliseconds: 500));

      } catch (e) {
        final browserWindow = await controller.getBrowserWindowById(windowId);
        final name = browserWindow?.name ?? 'ID:$windowId';
        failureList.add('$name - 启动异常: $e');
      }
    }

    // 统计结果
    final successCount = successList.length;
    final failureCount = failureList.length + failedToGetConfig.length;
    
    // 生成结果消息
    String resultMessage = '批量打开完成: 成功 $successCount 个';
    if (failureCount > 0) {
      resultMessage += ', 失败 $failureCount 个';
      
      // 收集所有错误信息
      List<String> allErrors = [...failedToGetConfig, ...failureList];
      
      if (allErrors.isNotEmpty) {
        // 只显示前3个错误，避免消息过长
        final displayErrors = allErrors.take(3).join('\n');
        resultMessage += '\n失败详情:\n$displayErrors';
        if (allErrors.length > 3) {
          resultMessage += '\n... 还有 ${allErrors.length - 3} 个错误';
        }
      }
    }

    // 根据参数决定是否清空选中状态
    if (clearSelection) {
      clearSelectedWindowIds();
    }
    
    return resultMessage;
  }

  // 关闭所有运行中的浏览器窗口
  Future<String> closeWindows() async {
    final browserManager = SimpleBrowserManager.instance;
    
    // 获取运行中的浏览器数量
    final runningBrowsers = browserManager.runningBrowsers;
    final runningCount = runningBrowsers.length;
    
    if (runningCount == 0) {
      return '没有运行中的浏览器窗口需要关闭';
    }

    try {
      // 调用stopAllBrowsers关闭所有浏览器
      await browserManager.stopAllBrowsers();
      
      return '已关闭所有运行中的浏览器窗口 ($runningCount 个)';
    } catch (e) {
      return '关闭浏览器时出现错误: $e';
    }
  }
}
