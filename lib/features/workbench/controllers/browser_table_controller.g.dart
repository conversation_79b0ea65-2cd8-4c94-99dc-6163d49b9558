// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_table_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$browserTableControllerHash() =>
    r'88dc13112cc9e8f52c9ddd8bf6c5b47f4f45166b';

/// See also [BrowserTableController].
@ProviderFor(BrowserTableController)
final browserTableControllerProvider = AutoDisposeNotifierProvider<
    BrowserTableController, BrowserSelectModel>.internal(
  BrowserTableController.new,
  name: r'browserTableControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserTableControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserTableController = AutoDisposeNotifier<BrowserSelectModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
