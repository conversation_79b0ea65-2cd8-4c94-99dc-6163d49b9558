// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'browser_window_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentPageHash() => r'6d42f9f07459d50fc8e85b689edc92825d772f5c';

/// See also [CurrentPage].
@ProviderFor(CurrentPage)
final currentPageProvider =
    NotifierProvider<CurrentPage, PaginationState>.internal(
  CurrentPage.new,
  name: r'currentPageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentPageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentPage = Notifier<PaginationState>;
String _$browserWindowControllerHash() =>
    r'aadefa2196340685e97d0ac59ef29799f2863713';

/// See also [BrowserWindowController].
@ProviderFor(BrowserWindowController)
final browserWindowControllerProvider = AutoDisposeAsyncNotifierProvider<
    BrowserWindowController, BrowserWindowModel>.internal(
  BrowserWindowController.new,
  name: r'browserWindowControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$browserWindowControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BrowserWindowController
    = AutoDisposeAsyncNotifier<BrowserWindowModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
