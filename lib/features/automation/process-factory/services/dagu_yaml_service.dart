import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';

/// Dagu YAML生成服务
/// 根据流程步骤列表生成符合dagu规范的yaml配置文件
class DaguYamlService {
  /// 生成dagu yaml内容
  static String generateYaml({
    required List<DaguOperation> operations,
    required String workflowName,
    String? description,
    Map<String, dynamic>? env,
  }) {
    if (operations.isEmpty) {
      throw ArgumentError('操作列表不能为空');
    }

    final buffer = StringBuffer();
    
    // 工作流基本信息
    buffer.writeln('# Dagu工作流配置文件');
    buffer.writeln('# 由Flutter流程设计器自动生成');
    buffer.writeln('');
    buffer.writeln('name: $workflowName');
    
    if (description?.isNotEmpty == true) {
      buffer.writeln('description: "$description"');
    }
    
    // 环境变量
    if (env?.isNotEmpty == true) {
      buffer.writeln('');
      buffer.writeln('env:');
      env!.forEach((key, value) {
        buffer.writeln('  $key: "$value"');
      });
    }
    
    // 步骤定义
    buffer.writeln('');
    buffer.writeln('steps:');
    
    for (int i = 0; i < operations.length; i++) {
      final operation = operations[i];
      final stepName = 'step_${i + 1}_${_sanitizeName(operation.name)}';
      
      buffer.writeln('  - name: $stepName');
      buffer.writeln('    desc: "${operation.name}"');
      
      // 根据操作类型生成不同的命令
      final command = _generateCommand(operation);
      buffer.writeln('    command: $command');
      
      // 添加依赖关系（除了第一个步骤）
      if (i > 0) {
        final prevStepName = 'step_${i}_${_sanitizeName(operations[i-1].name)}';
        buffer.writeln('    depends:');
        buffer.writeln('      - $prevStepName');
      }
      
      // 添加分类标签
      buffer.writeln('    tags:');
      buffer.writeln('      - category:${_sanitizeName(operation.category)}');
      
      if (i < operations.length - 1) {
        buffer.writeln('');
      }
    }
    
    return buffer.toString();
  }
  
  /// 根据操作生成具体的命令
  static String _generateCommand(DaguOperation operation) {
    // 检查是否还有未配置的参数
    final command = operation.command;
    final regex = RegExp(r'\$\{([^}]+)\}');
    final hasUnconfiguredParams = regex.hasMatch(command);
    
    if (hasUnconfiguredParams) {
      // 如果还有未配置的参数，用注释提示
      return 'echo "警告: 命令包含未配置的参数: $command"';
    }
    
    // 直接使用操作的命令配置
    return command;
  }
  
  /// 清理名称，使其符合yaml规范
  static String _sanitizeName(String name) {
    return name
        .replaceAll(' ', '_')
        .replaceAll('/', '_')
        .replaceAll('\\', '_')
        .replaceAll(':', '_')
        .replaceAll('*', '_')
        .replaceAll('?', '_')
        .replaceAll('"', '_')
        .replaceAll('<', '_')
        .replaceAll('>', '_')
        .replaceAll('|', '_')
        .toLowerCase();
  }
  
  /// 保存yaml文件到指定路径
  static Future<void> saveYamlFile({
    required String content,
    required String fileName,
    String? directory,
  }) async {
    // TODO: 实现文件保存逻辑
    // 这里需要根据你的文件保存策略来实现
    // 可能需要使用path_provider获取应用目录
    print('保存YAML文件: $fileName');
    print('内容:\n$content');
  }
} 
