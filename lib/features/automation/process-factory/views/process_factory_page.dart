import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:frontend_re/features/automation/process-factory/models/dagu_operation.dart';
import 'package:frontend_re/features/automation/process-factory/services/dagu_yaml_service.dart';
import 'package:frontend_re/features/automation/process-factory/views/components/common_edit_dialog.dart';
import 'package:frontend_re/widgets/custom_border_button.dart';
import 'package:frontend_re/widgets/painters/index.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';

// 导入分离出的组件
import 'components/index.dart';


// NotchedPanelPainter 已移动到 lib/widgets/painters/notched_panel_painters.dart





/// 主页面组件 - 流程工厂页面
/// 使用 HookConsumerWidget 监听全局状态变化
/// 布局原理：左右分栏布局，左侧操作选项，右侧流程设计器
class ProcessFactoryPage extends HookConsumerWidget {
  const ProcessFactoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    /// 监听全局拖拽状态 - 当有新的操作项被拖拽时，页面会自动重新构建
    final droppedItems = ref.watch(droppedItemsProvider);
    
    /// 搜索框控制器和搜索关键词状态
    final searchController = useTextEditingController();
    final searchKeyword = useState<String>('');
    
      return Scaffold(
      // backgroundColor: const Color(0xFFF8F9FA),
      body: Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            /// 左侧区域 - 带缺口的操作面板 + 返回按钮
            SizedBox(
              width: 300,
              child: Stack(
                children: [
                  // 带缺口的操作面板
                  Positioned(
                    top: 0, // 为返回按钮留出空间
                    left: 0, // 为返回按钮留出空间
                    right: 0,
                    bottom: 0,
                    child: CustomPaint(
                      painter: NotchedPanelPainter(),
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Color(0x00FFFFFF),
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                        ),
                        padding: const EdgeInsets.only(
                          left: 20, // 增加左边距避开缺口
                          // right: 16,
                          top: 22, // 增加上边距避开缺口
                          // bottom: 16,
                        ),
                        child: Column(
                            children: [
                              /// 标题区域
                              Row(
                                children: [
                                  const SizedBox(width: 56),
                                  /// 标题指示器 - 蓝色圆点
                                  Container(
                                    width: 16,
                                    height: 16,
                                    decoration: BoxDecoration(
                                      color: const Color(0xFF0C75F8),
                                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                                      border: Border.all(color: const Color(0xFFE9ECEF), width: 1),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  const Text(
                                    '操作选项',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF333333),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 26),
                              /// 搜索框
                              Padding(
                                padding: const EdgeInsets.only(right: 16),
                                child: SizedBox(
                                  height: 36,
                                  child: TextField(
                                    controller: searchController,
                                    onChanged: (value) {
                                      searchKeyword.value = value.trim();
                                    },
                                    style: const TextStyle(fontSize: 14),
                                    decoration: InputDecoration(
                                      hintText: '搜索操作项...',
                                      hintStyle: const TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 14,
                                      ),
                                      filled: true,
                                      fillColor: const Color(0xFFF3F4F8),
                                      contentPadding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: BorderSide.none,
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: BorderSide.none,
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(50),
                                        borderSide: const BorderSide(
                                          color: Color(0xFF0C75F8),
                                          width: 1,
                                        ),
                                      ),
                                      prefixIcon: const Icon(
                                        Icons.search,
                                        size: 16,
                                        color: Color(0xFF8D8E93),
                                      ),
                                      suffixIcon: searchKeyword.value.isNotEmpty
                                          ? IconButton(
                                              icon: const Icon(
                                                Icons.clear,
                                                size: 16,
                                                color: Color(0xFF8D8E93),
                                              ),
                                              onPressed: () {
                                                searchController.clear();
                                                searchKeyword.value = '';
                                              },
                                            )
                                          : null,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              /// 操作选项列表 - 可滚动的展开式列表
                              Expanded(
                                child: SingleChildScrollView(
                                  child: OperationOptionsWidget(
                                    searchKeyword: searchKeyword.value,
                                  ),
                                ),
                              ),
                            ],
                          ),
                      ),
                    ),
                  ),
                  
                  // 返回按钮 - 浮动在左上角
                  Positioned(
                    top: 0,
                    left: 0,
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.arrow_back),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 16),
            
            /// 右侧容器 - 流程设计器区域
            Expanded(
              child: Column(
                children: [
                  /// 上方固定高度容器 - 工具栏区域
                  Container(
                    height: 200, // 固定高度
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      borderRadius: BorderRadius.all(Radius.circular(20)),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          /// 工具栏标题 和按钮
                          Row(
                            children: [
                              Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF0C75F8),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              const SizedBox(width: 12),
                              const Text(
                                '工作流工具',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF333333),
                                ),
                              ),
                              const Spacer(),
                              CustomBorderButton(
                                iconPath: 'assets/svg/add.svg',
                                iconSize: 16,
                                iconColor: const Color(0xFF0C75F8),
                                onPressed: () {},
                              ),
                              const SizedBox(width: 16),
                              Text(
                                '当前步骤: ${droppedItems.length}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF8D8E93),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                          
                          /// 功能按钮区域
                          Expanded(
                            child: Row(
                              children: [
                                /// 生成YAML按钮
                                Expanded(
                                  child: _buildActionButton(
                                    context: context,
                                    ref: ref,
                                    icon: Icons.code,
                                    title: '生成YAML',
                                    subtitle: '导出工作流配置',
                                    color: const Color(0xFF0C75F8),
                                    enabled: droppedItems.isNotEmpty,
                                    onTap: () => _generateYaml(context, ref),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                
                                /// 预览按钮
                                Expanded(
                                  child: _buildActionButton(
                                    context: context,
                                    ref: ref,
                                    icon: Icons.preview,
                                    title: '预览流程',
                                    subtitle: '查看流程结构',
                                    color: const Color(0xFF28A745),
                                    enabled: droppedItems.isNotEmpty,
                                    onTap: () => _previewWorkflow(context, ref),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                
                                /// 清空按钮
                                Expanded(
                                  child: _buildActionButton(
                                    context: context,
                                    ref: ref,
                                    icon: Icons.clear_all,
                                    title: '清空流程',
                                    subtitle: '重新开始设计',
                                    color: const Color(0xFFDC3545),
                                    enabled: droppedItems.isNotEmpty,
                                    onTap: () => _clearWorkflow(context, ref),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  /// 下方自适应容器 - 流程设计器主体
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 50),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              /// 主要内容区域
                              if (droppedItems.isEmpty)
                                /// 空状态 - 显示拖拽提示区域
                                DragTarget<DaguOperation>(
                                  onAcceptWithDetails: (details) {
                                    ref.read(droppedItemsProvider.notifier).state = [details.data];
                                  },
                                  builder: (context, candidateData, rejectedData) {
                                    final isHovering = candidateData.isNotEmpty;
                                    return Container(
                                      width: double.infinity,
                                      height: 200,
                                      decoration: BoxDecoration(
                                        color: isHovering 
                                            ? const Color(0xFFE3F2FD) 
                                            : const Color(0xFFF8F9FA),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: isHovering 
                                              ? const Color(0xFF0C75F8) 
                                              : const Color(0xFFE9ECEF),
                                          width: isHovering ? 1 : 1,
                                        ),
                                      ),
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              isHovering ? Icons.add_circle : Icons.add_circle_outline,
                                              size: 48,
                                              color: isHovering 
                                                  ? const Color(0xFF0C75F8) 
                                                  : const Color(0xFFBDBDBD),
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              isHovering 
                                                  ? '松开鼠标添加第一个流程步骤' 
                                                  : '拖拽操作项到这里开始构建流程',
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: isHovering 
                                                    ? const Color(0xFF0C75F8) 
                                                    : const Color(0xFFBDBDBD),
                                                fontWeight: isHovering ? FontWeight.w500 : FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                )
                              else
                                /// 有内容状态
                                Column(
                                  children: [
                                    /// 顶部插入区域
                                    InsertDropTarget(
                                      insertIndex: 0,
                                      droppedItems: droppedItems,
                                      ref: ref,
                                      position: '开头',
                                    ),
                                    
                                    /// 可重排序列表
                                    ReorderableListView.builder(
                                      shrinkWrap: true,
                                      physics: const NeverScrollableScrollPhysics(),
                                      buildDefaultDragHandles: false,
                                      itemCount: droppedItems.length,
                                      proxyDecorator: (child, index, animation) {
                                        return AnimatedBuilder(
                                          animation: animation,
                                          builder: (context, child) {
                                            return Transform.scale(
                                              scale: 1.00,
                                              child: Transform.rotate(
                                                angle: 0.00,
                                                child: Material(
                                                  color: Colors.transparent,
                                                  elevation: 0,
                                                  borderRadius: BorderRadius.circular(12),
                                                  child: child!,
                                                ),
                                              ),
                                            );
                                          },
                                          child: child,
                                        );
                                      },
                                      itemExtent: null,
                                      padding: EdgeInsets.zero,
                                      onReorder: (int oldIndex, int newIndex) {
                                        if (oldIndex < newIndex) {
                                          newIndex -= 1;
                                        }
                                        final items = [...droppedItems];
                                        final item = items.removeAt(oldIndex);
                                        items.insert(newIndex, item);
                                        ref.read(droppedItemsProvider.notifier).state = items;
                                      },
                                      itemBuilder: (context, index) {
                                        return Column(
                                          key: ValueKey('step_wrapper_$index'),
                                          children: [
                                            ProcessStepCardSimple(
                                              key: ValueKey('step_${droppedItems[index].name}_$index'),
                                              operationItem: droppedItems[index],
                                              stepIndex: index,
                                              reorderableIndex: index,
                                              onDelete: () {
                                                final items = [...droppedItems];
                                                items.removeAt(index);
                                                ref.read(droppedItemsProvider.notifier).state = items;
                                              },
                                              onEdit: () {
                                                showDialog(
                                                  context: context,
                                                  builder: (context) => CommonEditDialog(
                                                    operation: droppedItems[index],
                                                    onSave: (updatedOperation) {
                                                      final items = [...droppedItems];
                                                      items[index] = updatedOperation;
                                                      ref.read(droppedItemsProvider.notifier).state = items;
                                                    },
                                                  ),
                                                );
                                              },
                                            ),
                                            
                                            if (index < droppedItems.length - 1)
                                              InsertDropTarget(
                                                insertIndex: index + 1,
                                                droppedItems: droppedItems,
                                                ref: ref,
                                                position: '第 ${index + 2} 个位置',
                                              ),
                                          ],
                                        );
                                      },
                                    ),
                                    
                                    /// 底部插入区域
                                    InsertDropTarget(
                                      insertIndex: droppedItems.length,
                                      droppedItems: droppedItems,
                                      ref: ref,
                                      position: '末尾',
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required bool enabled,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: enabled ? onTap : null,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: enabled ? color.withValues(alpha: 0.1) : const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: enabled ? color.withValues(alpha: 0.3) : const Color(0xFFE9ECEF),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: enabled ? color : const Color(0xFFBDBDBD),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: enabled ? color : const Color(0xFFBDBDBD),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: enabled ? color.withValues(alpha: 0.7) : const Color(0xFFBDBDBD),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 生成YAML配置
  void _generateYaml(BuildContext context, WidgetRef ref) {
    final operations = ref.read(droppedItemsProvider);
    if (operations.isEmpty) return;

    try {
      final yaml = DaguYamlService.generateYaml(
        operations: operations,
        workflowName: 'browser_automation_workflow',
        description: '浏览器自动化工作流程',
        env: {
          'BROWSER_TYPE': 'chrome',
          'TIMEOUT': '30',
          'HEADLESS': 'false',
        },
      );

      _showYamlDialog(context, yaml);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('生成YAML失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 显示YAML预览对话框
  void _showYamlDialog(BuildContext context, String yaml) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 800,
          height: 600,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.code, color: Color(0xFF0C75F8)),
                  const SizedBox(width: 8),
                  const Text(
                    'Dagu工作流配置',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFFE9ECEF)),
                  ),
                  child: SingleChildScrollView(
                    child: SelectableText(
                      yaml,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: 实现复制到剪贴板功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('已复制到剪贴板')),
                      );
                    },
                    icon: const Icon(Icons.copy),
                    label: const Text('复制'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: 实现保存文件功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('文件保存功能待实现')),
                      );
                    },
                    icon: const Icon(Icons.download),
                    label: const Text('下载'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 预览工作流程
  void _previewWorkflow(BuildContext context, WidgetRef ref) {
    final operations = ref.read(droppedItemsProvider);
    if (operations.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          height: 500,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.preview, color: Color(0xFF28A745)),
                  const SizedBox(width: 8),
                  const Text(
                    '工作流程预览',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: operations.length,
                  itemBuilder: (context, index) {
                    final operation = operations[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8F9FA),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: const Color(0xFF28A745),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  operation.name,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '分类: ${operation.categoryName}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF8D8E93),
                                  ),
                                ),
                                if (operation.command?.isNotEmpty == true)
                                  Text(
                                    '命令: ${operation.command}',
                                    style: const TextStyle(
                                      fontSize: 11,
                                      color: Color(0xFF0C75F8),
                                      fontFamily: 'monospace',
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 清空工作流程
  void _clearWorkflow(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清空'),
        content: const Text('确定要清空当前工作流程吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(droppedItemsProvider.notifier).state = [];
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('工作流程已清空')),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFDC3545),
            ),
            child: const Text('确认清空', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
