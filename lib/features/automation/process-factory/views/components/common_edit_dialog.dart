import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:frontend_re/widgets/custom_text_field.dart';
import 'package:frontend_re/widgets/primary_botton.dart';
import 'package:frontend_re/widgets/secondary_botton.dart';
import '../../models/dagu_operation.dart';

/// 通用编辑对话框 - 支持动态参数配置
class CommonEditDialog extends HookConsumerWidget {
  const CommonEditDialog({
    super.key, 
    required this.operation,
    required this.onSave,
  });

  final DaguOperation operation;
  final Function(DaguOperation updatedOperation) onSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 解析命令中的参数
    final parameters = _parseParameters(operation.command);
    
    // 为每个参数创建控制器
    final controllers = <String, TextEditingController>{};
    for (final param in parameters) {
      controllers[param] = useTextEditingController();
    }

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0C75F8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '配置参数 - ${operation.name}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // 操作描述
            if (operation.description?.isNotEmpty == true) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Color(0xFF0C75F8),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        operation.description!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF666666),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 命令预览
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF2D3748),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '命令模板:',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFE2E8F0),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  SelectableText(
                    operation.command,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Color(0xFF68D391),
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            
            // 参数配置区域
            if (parameters.isNotEmpty) ...[
              const Text(
                '参数配置:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(height: 12),
              
              // 参数列表
              Container(
                constraints: const BoxConstraints(maxHeight: 300),
                child: SingleChildScrollView(
                  child: Column(
                    children: parameters.map((param) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  param,
                                  style: const TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF4A5568),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFE2E8F0),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    '\${$param}',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      color: Color(0xFF718096),
                                      fontFamily: 'monospace',
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            CustomTextField(
                              controller: controllers[param]!,
                              hint: '请输入${_getParameterHint(param)}',
                              height: 12,
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ] else ...[
              // 无参数提示
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF0F4F8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 16,
                      color: Color(0xFF38A169),
                    ),
                    SizedBox(width: 8),
                    Text(
                      '此操作无需额外参数配置',
                      style: TextStyle(
                        fontSize: 13,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // 按钮区域
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SecondaryButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 12),
                PrimaryButton(
                  onPressed: () {
                    // 生成配置后的操作
                    final configuredOperation = _generateConfiguredOperation(
                      operation,
                      controllers,
                    );
                    onSave(configuredOperation);
                    Navigator.of(context).pop();
                  },
                  child: const Text('保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 解析命令中的参数
  List<String> _parseParameters(String command) {
    final regex = RegExp(r'\$\{([^}]+)\}');
    final matches = regex.allMatches(command);
    return matches.map((match) => match.group(1)!).toSet().toList()..sort();
  }

  /// 获取参数提示文本
  String _getParameterHint(String param) {
    switch (param.toUpperCase()) {
      case 'URL':
        return 'URL地址 (如: https://example.com)';
      case 'SELECTOR':
        return '页面元素选择器 (如: #id, .class, tag)';
      case 'KEY':
        return '按键名称 (如: Enter, Space, Tab)';
      case 'KEYS':
        return '组合键 (如: Ctrl+C, Alt+Tab)';
      case 'SECONDS':
        return '等待时间(秒) (如: 3, 5.5)';
      case 'TAB_INDEX':
        return '标签页索引 (如: 0, 1, 2)';
      case 'SHORTCUT':
        return '快捷键 (如: copy, paste, save)';
      case 'SPECIAL_KEY':
        return '特殊按键 (如: F1, F12, Escape)';
      case 'ATTRIBUTE':
        return '元素属性名 (如: href, src, class)';
      case 'COOKIE_NAME':
        return 'Cookie名称';
      case 'INPUT':
        return '输入数据';
      case 'OUTPUT':
        return '输出格式';
      case 'DATA':
        return '数据内容';
      case 'RULES':
        return '验证规则';
      case 'CONDITIONS':
        return '过滤条件';
      case 'FORMULA':
        return '计算公式';
      case 'FORMAT':
        return '数据格式';
      default:
        return param.toLowerCase();
    }
  }

  /// 生成配置后的操作
  DaguOperation _generateConfiguredOperation(
    DaguOperation operation,
    Map<String, TextEditingController> controllers,
  ) {
    String configuredCommand = operation.command;
    
    // 替换所有参数
    controllers.forEach((param, controller) {
      final value = controller.text.trim();
      if (value.isNotEmpty) {
        configuredCommand = configuredCommand.replaceAll(
          '\${$param}',
          value,
        );
      }
    });
    
    // 创建新的配置后操作（这里需要扩展DaguOperation支持自定义命令）
    return DaguOperationConfigured(
      operationId: operation.operationId,
      configuredCommand: configuredCommand,
      parameters: Map.fromEntries(
        controllers.entries.map(
          (entry) => MapEntry(entry.key, entry.value.text.trim()),
        ),
      ),
    );
  }
}

/// 配置后的操作类 - 扩展DaguOperation
class DaguOperationConfigured extends DaguOperation {
  final String configuredCommand;
  final Map<String, String> parameters;
  
  const DaguOperationConfigured({
    required super.operationId,
    required this.configuredCommand,
    required this.parameters,
  });
  
  @override
  String get command => configuredCommand;
  
  @override
  String toString() {
    return 'DaguOperationConfigured(id: $operationId, command: $configuredCommand, params: $parameters)';
  }
}
