import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../models/dagu_operation.dart';

/// 流程工厂控制器 - 管理拖拽流程的状态
/// 使用 Riverpod 的 StateProvider 实现状态共享
/// 原理：所有拖拽操作都会更新这个状态，UI会自动重新构建
final droppedItemsProvider = StateProvider<List<DaguOperation>>((ref) => []);

/// 可选：历史记录提供者 - 支持撤销/重做功能
final historyProvider = StateProvider<List<List<DaguOperation>>>((ref) => []);

/// 可选：搜索查询提供者
final searchQueryProvider = StateProvider<String>((ref) => ''); 
