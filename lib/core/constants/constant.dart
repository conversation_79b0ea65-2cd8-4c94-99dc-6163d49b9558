import 'package:frontend_re/widgets/custom_checkbox_group.dart';

enum PlatformType {
  eCommerce,     // 电商平台
  socialMedia,   // 社交媒体
  payment,       // 支付平台
  others         // 其他（如果需明确"平台"可写为 otherPlatforms）
}

final List<Map<String, dynamic>> osOptions = [
  {'name': 'Windows 11'},
  {'name': 'Windows 10'},
  {'name': 'macOS 14'},
  {'name': 'macOS 13'},
  {'name': 'macOS 12'},
  {'name': 'macOS 11'},
  {'name': 'macOS 10'},
];

final List<Map<String, dynamic>> chromeVersionOptions = [
  {'name': '120'},
  {'name': '119'},
  {'name': '118'},
  {'name': '117'},
];

final List<Map<String, dynamic>> websiteOptions = [
  {
    'name': 'tiktok小店',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/tkshop.svg',
    'details': [
      {'name': '全球', 'url': 'https://www.tiktok.com/shop'},
      {'name': '美国', 'url': 'https://www.tiktok.com/shop?lang=en'},
      {'name': '英国', 'url': 'https://www.tiktok.com/shop?lang=en-GB'},
      {'name': '越南', 'url': 'https://www.tiktok.com/shop?lang=vi-VN'},
      {'name': '印尼', 'url': 'https://www.tiktok.com/shop?lang=id-ID'},
      {'name': '马来西亚', 'url': 'https://www.tiktok.com/shop?lang=ms-MY'},
      {'name': '泰国', 'url': 'https://www.tiktok.com/shop?lang=th-TH'},
      {'name': '菲律宾', 'url': 'https://www.tiktok.com/shop?lang=fil-PH'},
      {'name': '新加坡', 'url': 'https://www.tiktok.com/shop?lang=en-SG'},
      {'name': '西班牙', 'url': 'https://www.tiktok.com/shop?lang=es-ES'},
      {'name': '德国', 'url': 'https://www.tiktok.com/shop?lang=de-DE'},
      {'name': '墨西哥', 'url': 'https://www.tiktok.com/shop?lang=es-MX'},
      {'name': '爱尔兰', 'url': 'https://www.tiktok.com/shop?lang=en-IE'},
      {'name': '法国', 'url': 'https://www.tiktok.com/shop?lang=fr-FR'},
      {'name': '意大利', 'url': 'https://www.tiktok.com/shop?lang=it-IT'},
      {'name': '巴西', 'url': 'https://www.tiktok.com/shop?lang=pt-BR'},
      {'name': '日本', 'url': 'https://www.tiktok.com/shop?lang=ja-JP'},
    ],
  },
  {
    'name': '亚马逊',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/amazon.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.amazon.com'},
      {'name': '加拿大', 'url': 'https://www.amazon.ca'},
      {'name': '墨西哥', 'url': 'https://www.amazon.com.mx'},
      {'name': '英国', 'url': 'https://www.amazon.co.uk'},
      {'name': '德国', 'url': 'https://www.amazon.de'},
      {'name': '法国', 'url': 'https://www.amazon.fr'},
      {'name': '意大利', 'url': 'https://www.amazon.it'},
      {'name': '西班牙', 'url': 'https://www.amazon.es'},
      {'name': '波兰', 'url': 'https://www.amazon.pl'},
      {'name': '瑞典', 'url': 'https://www.amazon.se'},
      {'name': '比利时', 'url': 'https://www.amazon.com.be'},
      {'name': '荷兰', 'url': 'https://www.amazon.nl'},
      {'name': '日本', 'url': 'https://www.amazon.co.jp'},
      {'name': '新加坡', 'url': 'https://www.amazon.sg'},
      {'name': '澳大利亚', 'url': 'https://www.amazon.com.au'},
      {'name': '阿联酋', 'url': 'https://www.amazon.ae'},
      {'name': '沙特阿拉伯', 'url': 'https://www.amazon.sa'},
      {'name': '埃及', 'url': 'https://www.amazon.eg'},
      {'name': '印度', 'url': 'https://www.amazon.in'},
      {'name': '土耳其', 'url': 'https://www.amazon.com.tr'},
      {'name': '巴西', 'url': 'https://www.amazon.com.br'},
    ],
  },
  {
    'name': 'shopee',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/shopee.svg',
    'details': [
      {'name': '新加坡', 'url': 'https://shopee.sg'},
      {'name': '马来西亚', 'url': 'https://shopee.com.my'},
      {'name': '泰国', 'url': 'https://shopee.co.th'},
      {'name': '菲律宾', 'url': 'https://shopee.ph'},
      {'name': '越南', 'url': 'https://shopee.vn'},
      {'name': '印度尼西亚', 'url': 'https://shopee.co.id'},
      {'name': '台湾', 'url': 'https://shopee.tw'},
      {'name': '巴西', 'url': 'https://shopee.com.br'},
      {'name': '墨西哥', 'url': 'https://shopee.com.mx'},
      {'name': '波兰', 'url': 'https://shopee.pl'},
    ],
  },
  {
    'name': 'ebay',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/ebay.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.ebay.com'},
      {'name': '英国', 'url': 'https://www.ebay.co.uk'},
      {'name': '德国', 'url': 'https://www.ebay.de'},
      {'name': '澳大利亚', 'url': 'https://www.ebay.com.au'},
      {'name': '加拿大', 'url': 'https://www.ebay.ca'},
      {'name': '法国', 'url': 'https://www.ebay.fr'},
      {'name': '意大利', 'url': 'https://www.ebay.it'},
      {'name': '西班牙', 'url': 'https://www.ebay.es'},
      {'name': '奥地利', 'url': 'https://www.ebay.at'},
      {'name': '瑞士', 'url': 'https://www.ebay.ch'},
      {'name': '比利时', 'url': 'https://www.ebay.be'},
      {'name': '爱尔兰', 'url': 'https://www.ebay.ie'},
      {'name': '荷兰', 'url': 'https://www.ebay.nl'},
      {'name': '波兰', 'url': 'https://www.ebay.pl'},
    ],
  },
  {
    'name': 'PayPal',
    'type': PlatformType.payment,
    'icon': 'assets/svg/platform/paypal.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.paypal.com/us/'},
      {'name': '加拿大', 'url': 'https://www.paypal.com/ca/'},
      {'name': '英国', 'url': 'https://www.paypal.com/uk/'},
      {'name': '澳大利亚', 'url': 'https://www.paypal.com/au/'},
      {'name': '德国', 'url': 'https://www.paypal.com/de/'},
      {'name': '法国', 'url': 'https://www.paypal.com/fr/'},
      {'name': '意大利', 'url': 'https://www.paypal.com/it/'},
      {'name': '西班牙', 'url': 'https://www.paypal.com/es/'},
      {'name': '荷兰', 'url': 'https://www.paypal.com/nl/'},
      {'name': '新西兰', 'url': 'https://www.paypal.com/nz/'},
      {'name': '瑞士', 'url': 'https://www.paypal.com/ch/'},
      {'name': '爱尔兰', 'url': 'https://www.paypal.com/ie/'},
      {'name': '瑞典', 'url': 'https://www.paypal.com/se/'},
      {'name': '挪威', 'url': 'https://www.paypal.com/no/'},
      {'name': '比利时', 'url': 'https://www.paypal.com/be/'},
      {'name': '日本', 'url': 'https://www.paypal.com/jp/'},
      {'name': '新加坡', 'url': 'https://www.paypal.com/sg/'},
      {'name': '香港', 'url': 'https://www.paypal.com/hk/'},
      {'name': '印度', 'url': 'https://www.paypal.com/in/'},
      {'name': '以色列', 'url': 'https://www.paypal.com/il/'},
      {'name': '巴西', 'url': 'https://www.paypal.com/br/'},
      {'name': '墨西哥', 'url': 'https://www.paypal.com/mx/'},
      {'name': '阿根廷', 'url': 'https://www.paypal.com/ar/'},
      {'name': '南非', 'url': 'https://www.paypal.com/za/'},

    ],
  },
  {
    'name': '沃尔玛',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/walmart.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.walmart.com/'},
      {'name': '墨西哥', 'url': 'https://www.walmart.com.mx'},
    ],
  },
  {
    'name': '速卖通',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/aliexpress.svg',
    'details': [
      {'name': '全球', 'url': 'https://www.aliexpress.com'},
      {'name': '俄罗斯', 'url': 'https://www.aliexpress.ru'},
    ],
  },
  {
    'name': 'MercadoLibre',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/mercadolibre.svg',
    'details': [
      {'name': '阿根廷', 'url': 'https://www.mercadolibre.com.ar'},
      {'name': '墨西哥', 'url': 'https://www.mercadolibre.com.mx'},
      {'name': '巴西', 'url': 'https://www.mercadolivre.com.br'},
      {'name': '哥伦比亚', 'url': 'https://www.mercadolibre.com.co'},
      {'name': '智利', 'url': 'https://www.mercadolibre.cl'},
      {'name': '乌拉圭', 'url': 'https://www.mercadolibre.com.uy'},
      {'name': '秘鲁', 'url': 'https://www.mercadolibre.com.pe'},
    ],
  },
  {
    'name': 'Lazada',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/lazada.svg',
    'details': [
      {'name': '新加坡', 'url': 'https://www.lazada.sg'},
      {'name': '马来西亚', 'url': 'https://www.lazada.com.my'},
      {'name': '菲律宾', 'url': 'https://www.lazada.com.ph'},
      {'name': '泰国', 'url': 'https://www.lazada.co.th'},
      {'name': '越南', 'url': 'https://www.lazada.vn'},
      {'name': '印度尼西亚', 'url': 'https://www.lazada.co.id'},
    ],
  },
  {
    'name': 'Jumia',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/jumia.png',
    'details': [
      {'name': '尼日利亚', 'url': 'https://www.jumia.com.ng'},
      {'name': '埃及', 'url': 'https://www.jumia.com.eg'},
      {'name': '肯尼亚', 'url': 'https://www.jumia.co.ke'},
      {'name': '摩洛哥', 'url': 'https://www.jumia.ma'},
      {'name': '南非', 'url': 'https://www.jumia.co.za'},
      {'name': '科特迪瓦', 'url': 'https://www.jumia.ci'},
    ],
  },
  {
    'name': 'Rakuten',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/rakuten.png',
    'details': [
      {'name': '日本', 'url': 'https://www.rakuten.co.jp'},
      {'name': '法国', 'url': 'https://fr.shopping.rakuten.com'},
      {'name': '德国', 'url': 'https://www.rakuten.de'},
    ],
  },
  {
    'name': 'Wowma',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/wowma.svg',
    'details': [
      {'name': '日本', 'url': 'https://wowma.shop'},
    ],
  },
  {
    'name': '雅虎Shopping',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/yahoo.svg',
    'details': [
      {'name': '日本', 'url': 'https://shopping.yahoo.co.jp'},
    ],
  },
  {
    'name': 'MyMall',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/mymall.png',
    'details': [
      {'name': '俄罗斯', 'url': 'https://mymall.ru'},
    ],
  },
  {
    'name': 'Rakuma',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/rakuma.png',
    'details': [
      {'name': '日本', 'url': 'https://fril.jp'},
    ],
  },
  {
    'name': 'Yandex',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/yandex.svg',
    'details': [
      {'name': '全球', 'url': 'https://yandex.com'},
      {'name': '俄罗斯', 'url': 'https://yandex.ru'},
    ],
  },
  {
    'name': 'Wayfair',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/wayfair.png',
    'details': [
      {'name': '美国', 'url': 'https://www.wayfair.com'},
      {'name': '加拿大', 'url': 'https://www.wayfair.ca'},
      {'name': '英国', 'url': 'https://www.wayfair.co.uk'},
      {'name': '爱尔兰', 'url': 'https://www.wayfair.ie'},
    ],
  },
  {
    'name': 'Kogan',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/kogan.png',
    'details': [
      {'name': '澳大利亚', 'url': 'https://www.kogan.com/au'},
    ],
  },
  {
    'name': 'Daraz',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/daraz.png',
    'details': [
      {'name': '巴基斯坦', 'url': 'https://www.daraz.pk'},
      {'name': '孟加拉国', 'url': 'https://www.daraz.com.bd'},
      {'name': '斯里兰卡', 'url': 'https://www.daraz.lk'},
      {'name': '尼泊尔', 'url': 'https://www.daraz.com.np'},
      {'name': '缅甸', 'url': 'https://www.daraz.com.mm'},
    ],
  },
  {
    'name': 'Darty',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/darty.svg',
    'details': [
      {'name': '法国', 'url': 'https://www.darty.com'},
    ],
  },
  {
    'name': 'Fnac',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/fnac.svg',
    'details': [
      {'name': '法国', 'url': 'https://www.fnac.com'},
      {'name': '西班牙', 'url': 'https://www.fnac.es'},
      {'name': '葡萄牙', 'url': 'https://www.fnac.pt'},
      {'name': '比利时', 'url': 'https://www.fnac.be'},
    ],
  },
  {
    'name': '家乐福',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/carrefour.svg',
    'details': [
      {'name': '西班牙', 'url': 'https://www.carrefour.es'},
    ],
  },
  {
    'name': 'Allegro',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/allegro.svg',
    'details': [
      {'name': '波兰', 'url': 'https://www.allegro.pl'},
    ],
  },
  {
    'name': 'Qoo10',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/qoo10.svg',
    'details': [
      {'name': '日本', 'url': 'https://www.qoo10.jp'},
    ],
  },
  {
    'name': 'B2W',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/b2w.png',
    'details': [
      {'name': '巴西', 'url': 'https://www.americanas.com.br'},
    ],
  },
  {
    'name': 'Flipkart',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/flipkart.png',
    'details': [
      {'name': '印度', 'url': 'https://www.flipkart.com'},
    ],
  },
  {
    'name': 'Newegg',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/newegg.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.newegg.com'},
      {'name': '加拿大', 'url': 'https://www.newegg.ca'},
      {'name': '英国', 'url': 'https://www.newegg.co.uk'},
      {'name': '澳大利亚', 'url': 'https://www.newegg.com.au'},
      {'name': '新加坡', 'url': 'https://www.newegg.sg'},
      {'name': '爱尔兰', 'url': 'https://www.newegg.ie'},
    ],
  },
  {
    'name': 'Temu',
    'type': PlatformType.eCommerce,
    'icon': 'assets/images/platform/temu.png',
    'details': [
      {'name': '美国', 'url': 'https://www.temu.com'},
      {'name': '加拿大', 'url': 'https://www.temu.ca'},
      {'name': '英国', 'url': 'https://www.temu.co.uk'},
      {'name': '德国', 'url': 'https://www.temu.de'},
      {'name': '法国', 'url': 'https://www.temu.fr'},
      {'name': '意大利', 'url': 'https://www.temu.it'},
      {'name': '西班牙', 'url': 'https://www.temu.es'},
      {'name': '澳大利亚', 'url': 'https://www.temu.com.au'},
      {'name': '新西兰', 'url': 'https://www.temu.co.nz'},
      {'name': '日本', 'url': 'https://www.temu.jp'},
    ],
  },
  {
    'name': 'SHEIN',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/shein.svg',
    'details': [
      {'name': '美国', 'url': 'https://www.shein.com'},
      {'name': '巴西', 'url': 'https://www.shein.com.br'},
      {'name': '法国', 'url': 'https://www.shein.com/fr'},
      {'name': '德国', 'url': 'https://www.shein.com/de'},
      {'name': '西班牙', 'url': 'https://www.shein.com/es'},
      {'name': '意大利', 'url': 'https://www.shein.com/it'},
      {'name': '澳大利亚', 'url': 'https://www.shein.com.au'},
      {'name': '日本', 'url': 'https://www.shein.com/jp'},
      {'name': '阿联酋', 'url': 'https://www.shein.com/ae'},
      {'name': '沙特阿拉伯', 'url': 'https://www.shein.com/sa'},
      {'name': '墨西哥', 'url': 'https://www.shein.com.mx'},
    ],
  },
  {
    'name': 'Zalando',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/zalando.svg',
    'details': [
      {'name': '德国', 'url': 'https://www.zalando.de'},
      {'name': '奥地利', 'url': 'https://www.zalando.at'},
      {'name': '瑞士', 'url': 'https://www.zalando.ch'},
      {'name': '法国', 'url': 'https://www.zalando.fr'},
      {'name': '比利时', 'url': 'https://www.zalando.be'},
      {'name': '荷兰', 'url': 'https://www.zalando.nl'},
      {'name': '意大利', 'url': 'https://www.zalando.it'},
      {'name': '西班牙', 'url': 'https://www.zalando.es'},
      {'name': '波兰', 'url': 'https://www.zalando.pl'},
      {'name': '瑞典', 'url': 'https://www.zalando.se'},
      {'name': '丹麦', 'url': 'https://www.zalando.dk'},
      {'name': '芬兰', 'url': 'https://www.zalando.fi'},
      {'name': '挪威', 'url': 'https://www.zalando.no'},
    ],
  },
  {
    'name': '淘宝',
    'type': PlatformType.eCommerce,
    'icon': 'assets/svg/platform/taobao.svg',
    'details': [
      {'name': '中国', 'url': 'https://www.taobao.com'},
    ],
  },
  {
    'name': '小红书',
    'type': PlatformType.socialMedia,
    'icon': 'assets/svg/platform/xiaohongshu.svg',
    'details': [
      {'name': '中国', 'url': 'https://www.xiaohongshu.com'},
    ],
  },
  {
    'name': '快手',
    'type': PlatformType.socialMedia,
    'icon': 'assets/svg/platform/kuaishou.svg',
    'details': [
      {'name': '澳大利亚', 'url': 'https://www.kuaishou.com'},
    ],
  },
];

// Define a map to hold website names and their corresponding icons
final List<Map<String, dynamic>> proxyOptions = [
  {'name': 'http'},
  {'name': 'https'}, // Example icon, replace as needed
  {'name': 'socks5'},
  {'name': 'none'},
];

// final List<String> chromeVersionOptions =
//         List.generate(5, (index) => (127 - index).toString());
final List<Map<String, dynamic>> languageLocaleOptions = [
  {'name': 'zh-CN'},
  {'name': 'en-US'},
  {'name': 'es-ES'},
  {'name': 'fr-FR'},
  {'name': 'de-DE'},
  {'name': 'ja-JP'},
  {'name': 'ko-KR'},
  {'name': 'ru-RU'},
  {'name': 'pt-BR'},
  {'name': 'ar-SA'},
];

final List<Map<String, dynamic>> localeOptions = [
  {'name': 'zh-CN'},
  {'name': 'en-US'},
  {'name': 'es-ES'},
  {'name': 'fr-FR'},
  {'name': 'de-DE'},
  {'name': 'ja-JP'},
  {'name': 'ko-KR'},
  {'name': 'ru-RU'},
  {'name': 'pt-BR'},
  {'name': 'ar-SA'},
];

final List<Map<String, dynamic>> webGL = [
  {'name': 'Google Inc. (AMD)'},
  {'name': 'Google Inc. (Intel)'},
  {'name': 'Google Inc. (NVIDIA)'},
  {'name': 'ARM'},
];

final List<Map<String, dynamic>> timezone = [
  {'name': 'Asia/Shanghai'},
  {'name': 'America/Adak'},
  {'name': 'Asia/Tomsk'},
  {'name': 'Europe/Dublin'},
  {'name': 'Pacific/Tongatapu'},
  {'name': 'Africa/Bangui'},
];

final List<Map<String, dynamic>> resolution = [
  {'name': '750*1334'},
  {'name': '800*600'},
  {'name': '1024*600'},
  {'name': '1152*864'},
  {'name': '1280*720'},
  {'name': '1280*1024'},
  {'name': '1360*768'},
];

final List<Map<String, dynamic>> memory = [
  {'name': '1'},
  {'name': '2'},
  {'name': '4'},
  {'name': '8'},
];

final List<Map<String, dynamic>> kernel = [
  {'name': '2'},
  {'name': '4'},
  {'name': '6'},
  {'name': '8'},
  {'name': '10'},
  {'name': '12'},
  {'name': '16'},
  {'name': '20'},
];

final Map<String, List<String>> vendors = {
  'Google Inc. (Intel)': [
    'ANGLE (Intel, Intel(R) HD Graphics 520 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) HD Graphics 5300 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_4_1 ps_4_1)',
    'ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) HD Graphics Family Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) HD Graphics 4400 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8935)',
    'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-26.20.100.7870)',
    'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)',
    'ANGLE (Intel, Intel(R) HD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)',
    'ANGLE (Intel, Intel(R) HD Graphics 530 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9466)',
    'ANGLE (Intel, Intel(R) HD Graphics 5500 Direct3D11 vs_5_0 ps_5_0, D3D11-20.19.15.5126)',
    'ANGLE (Intel, Intel(R) HD Graphics 6000 Direct3D11 vs_5_0 ps_5_0, D3D11-20.19.15.5126)',
    'ANGLE (Intel, Intel(R) HD Graphics 610 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9466)',
    'ANGLE (Intel, Intel(R) HD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9168)',
    'ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6589)',
    'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.9126)',
    'ANGLE (Intel, Mesa Intel(R) UHD Graphics 620 (KBL GT2), OpenGL 4.6 (Core Profile) Mesa 21.2.2)',
  ],
  'Google Inc. (AMD)': [
    'ANGLE (AMD, AMD Radeon (TM) R9 370 Series Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (AMD, AMD Radeon HD 7700 Series Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (AMD, ATI Mobility Radeon HD 4330 Direct3D11 vs_4_1 ps_4_1)',
    'ANGLE (AMD, ATI Mobility Radeon HD 4500 Series Direct3D11 vs_4_1 ps_4_1)',
    'ANGLE (AMD, ATI Mobility Radeon HD 5000 Series Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (AMD, ATI Mobility Radeon HD 5400 Series Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (AMD, AMD, Radeon (TM) RX 470 Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.1034.6)',
    'ANGLE (AMD, AMD, AMD Radeon(TM) Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.14028.11002)',
    'ANGLE (AMD, AMD, AMD Radeon RX 5700 XT Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13025.1000)',
    'ANGLE (AMD, AMD, AMD Radeon RX 6900 XT Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13011.1004)',
    'ANGLE (AMD, AMD, AMD Radeon(TM) Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.13002.23)',
  ],
  'Google Inc. (NVIDIA)': [
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Ti Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce RTX 2070 SUPER Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro K600 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro M1000M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 760 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 760 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 770 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 780 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 850M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 850M Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 860M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 950 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 950 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 950M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 950M Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 960 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 960 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 960M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 960M Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 970 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 970 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 980 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 980 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 980 Ti Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce GTX 980M Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce MX130 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce MX150 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce MX230 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce MX250 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D9Ex vs_3_0 ps_3_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 SUPER Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA GeForce RTX 2070 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro K620 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro FX 380 Direct3D11 vs_4_0 ps_4_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro NVS 295 Direct3D11 vs_4_0 ps_4_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P1000 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P2000 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P400 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P4000 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P600 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA Quadro P620 Direct3D11 vs_5_0 ps_5_0)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6079)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.13.6881)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 970 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1050 Ti/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 460.73.01)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1050 Ti/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 460.80)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1050/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1060 6GB/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1080 Ti/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 1650/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 650/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 750 Ti/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 860M/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce GTX 950M/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce MX150/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, GeForce RTX 2070/PCIe/SSE2, OpenGL 4.5 core)',
    'ANGLE (NVIDIA, NVIDIA Corporation, NVIDIA GeForce GTX 660/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 470.57.02)',
    'ANGLE (NVIDIA, NVIDIA Corporation, NVIDIA GeForce RTX 2060 SUPER/PCIe/SSE2, OpenGL 4.5.0 NVIDIA 470.63.01)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1050 Ti Direct3D9Ex vs_3_0 ps_3_0, nvd3dumx.dll-26.21.14.4250)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1060 5GB Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7168)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7212)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1070 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6677)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1080 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7111)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1650 Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7212)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1650 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7111)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1660 SUPER Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7196)',
    'ANGLE (NVIDIA, NVIDIA, NVIDIA GeForce GTX 1660 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-30.0.14.7196)',
  ],
  'ARM': [
    'Mali-T880',
    'Mali-G52',
    'Mali-G72',
  ]
};

enum WebRTC { forward, replace, real, disable }

enum Location { ask, allow, disable }

// WebRTC 选项定义
final List<CheckBoxItem> webRTCOptions = [
  const CheckBoxItem(label: '转发', value: 'forward'),
  const CheckBoxItem(label: '替换', value: 'replace'),
  const CheckBoxItem(label: '真实', value: 'real'),
  const CheckBoxItem(label: '禁用', value: 'disable'),
];

//canvas 选项定义
final List<CheckBoxItem> canvasOptions = [
  const CheckBoxItem(label: '噪音', value: 'true'),
  const CheckBoxItem(label: '真实', value: 'false')
];

//webGL 选项定义
final List<CheckBoxItem> webGLOptions = [
  const CheckBoxItem(label: '噪音', value: 'true'),
  const CheckBoxItem(label: '真实', value: 'false')
];


final List<Map<String, dynamic>> languageOptions = [
  {'name': 'zh-CN'},
  {'name': 'en-US'},
  {'name': 'es-ES'},
  {'name': 'fr-FR'},
  {'name': 'de-DE'},
];

final List<Map<String, dynamic>> timezoneOptions = [
  {'name': 'Asia/Shanghai'},
  {'name': 'America/Adak'},
  {'name': 'Asia/Tomsk'},
  {'name': 'Europe/Dublin'},
];
